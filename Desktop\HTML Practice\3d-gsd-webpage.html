<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D German Shepherd Dog</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            perspective: 1000px;
            overflow: hidden;
        }

        .container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .scene {
            width: 400px;
            height: 400px;
            position: relative;
            transform-style: preserve-3d;
            animation: rotate 20s infinite linear;
        }

        .cube {
            width: 400px;
            height: 400px;
            position: relative;
            transform-style: preserve-3d;
        }

        .face {
            position: absolute;
            width: 400px;
            height: 400px;
            border: 2px solid #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        .front {
            background-image: url('https://images.unsplash.com/photo-1551717743-49959800b1f6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80');
            transform: rotateY(0deg) translateZ(200px);
        }

        .back {
            background-image: url('https://images.unsplash.com/photo-1605568427561-40dd23c2acea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80');
            transform: rotateY(180deg) translateZ(200px);
        }

        .right {
            background-image: url('https://images.unsplash.com/photo-1589941013453-ec89f33b5e95?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80');
            transform: rotateY(90deg) translateZ(200px);
        }

        .left {
            background-image: url('https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80');
            transform: rotateY(-90deg) translateZ(200px);
        }

        .top {
            background-image: url('https://images.unsplash.com/photo-1587300003388-59208cc962cb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80');
            transform: rotateX(90deg) translateZ(200px);
        }

        .bottom {
            background-image: url('https://images.unsplash.com/photo-1596492784531-6e6eb5ea9993?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80');
            transform: rotateX(-90deg) translateZ(200px);
        }

        @keyframes rotate {
            0% {
                transform: rotateX(0deg) rotateY(0deg);
            }
            25% {
                transform: rotateX(0deg) rotateY(90deg);
            }
            50% {
                transform: rotateX(0deg) rotateY(180deg);
            }
            75% {
                transform: rotateX(0deg) rotateY(270deg);
            }
            100% {
                transform: rotateX(0deg) rotateY(360deg);
            }
        }

        .title {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 3rem;
            font-weight: bold;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.7);
            z-index: 10;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: translateX(-50%) scale(1);
            }
            50% {
                opacity: 0.8;
                transform: translateX(-50%) scale(1.05);
            }
        }

        .controls {
            position: absolute;
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 10;
        }

        .control-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .floating-bone {
            position: absolute;
            width: 30px;
            height: 30px;
            background: white;
            border-radius: 50%;
            animation: float 6s infinite ease-in-out;
            opacity: 0.7;
        }

        .bone1 { top: 20%; left: 10%; animation-delay: 0s; }
        .bone2 { top: 60%; right: 15%; animation-delay: 2s; }
        .bone3 { bottom: 30%; left: 20%; animation-delay: 4s; }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">3D German Shepherd Gallery</h1>
        
        <div class="scene" id="scene">
            <div class="cube">
                <div class="face front"></div>
                <div class="face back"></div>
                <div class="face right"></div>
                <div class="face left"></div>
                <div class="face top"></div>
                <div class="face bottom"></div>
            </div>
        </div>

        <div class="controls">
            <button class="control-btn" onclick="pauseAnimation()">Pause</button>
            <button class="control-btn" onclick="resumeAnimation()">Resume</button>
            <button class="control-btn" onclick="speedUp()">Speed Up</button>
            <button class="control-btn" onclick="slowDown()">Slow Down</button>
        </div>

        <div class="floating-elements">
            <div class="floating-bone bone1">🦴</div>
            <div class="floating-bone bone2">🦴</div>
            <div class="floating-bone bone3">🦴</div>
        </div>
    </div>

    <script>
        let animationSpeed = 20;
        let isPaused = false;
        const scene = document.getElementById('scene');

        function pauseAnimation() {
            scene.style.animationPlayState = 'paused';
            isPaused = true;
        }

        function resumeAnimation() {
            scene.style.animationPlayState = 'running';
            isPaused = false;
        }

        function speedUp() {
            if (animationSpeed > 5) {
                animationSpeed -= 5;
                scene.style.animationDuration = animationSpeed + 's';
            }
        }

        function slowDown() {
            if (animationSpeed < 60) {
                animationSpeed += 5;
                scene.style.animationDuration = animationSpeed + 's';
            }
        }

        // Mouse interaction
        document.addEventListener('mousemove', (e) => {
            if (!isPaused) {
                const x = (e.clientX / window.innerWidth - 0.5) * 20;
                const y = (e.clientY / window.innerHeight - 0.5) * 20;
                scene.style.transform = `rotateX(${y}deg) rotateY(${x}deg)`;
            }
        });

        // Touch interaction for mobile
        document.addEventListener('touchmove', (e) => {
            if (!isPaused) {
                const touch = e.touches[0];
                const x = (touch.clientX / window.innerWidth - 0.5) * 20;
                const y = (touch.clientY / window.innerHeight - 0.5) * 20;
                scene.style.transform = `rotateX(${y}deg) rotateY(${x}deg)`;
            }
        });
    </script>
</body>
</html>
