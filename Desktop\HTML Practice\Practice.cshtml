<!DOCTYPE html>
<html>
<head>
<title> Quick expenese tracker</title>
<style>
body{ font-family: sans-serif; padding: 20px;}
input, button{ padding:10px; margin: 5px;}
</style>
</head>
<body>
<!-- Input fields for description and and amount-->
<h2> Expense tracker</h2>
<input id = "desc" placeholder = "description"/>
<input id = "amt" type = "number" placeholder = "Amount"/>
<!-- button calls Js functions 'addExpense()' when clicked-->
<button onclick = " addExpense()">Add</button>
<!--This is where you added expenses will show up-->
<ul id ="list></ul>
<!--shows the total sum of all expenses-->
<p> Total: <span id = "total">0</span></p>
<!--connects the javascript logic -->
<script> src = "script.js"></script>
</body>
</html>
